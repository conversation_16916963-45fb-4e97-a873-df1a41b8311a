# Developer Portfolio Website

A modern, responsive portfolio website built with Next.js 15, TypeScript, and Tailwind CSS. Features a clean design, dark mode support, and optimized performance.

## ✨ Features

- **Modern Design**: Clean, minimal, and professional layout
- **Responsive**: Fully responsive design that works on all devices
- **Dark Mode**: Toggle between light and dark themes
- **SEO Optimized**: Built-in SEO with meta tags and structured data
- **Performance**: Optimized for speed and Core Web Vitals
- **Accessibility**: WCAG compliant with proper ARIA labels
- **Type Safe**: Built with TypeScript for better development experience

## 🚀 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React & Heroicons
- **Fonts**: Geist Sans & Geist Mono
- **Deployment**: Vercel (recommended)

## 📁 Project Structure

```
src/
├── app/                    # App Router pages
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── projects/          # Projects page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── footer.tsx         # Footer component
│   ├── header.tsx         # Header with navigation
│   └── theme-provider.tsx # Dark mode provider
└── lib/                   # Utility functions
```

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd developer-portfolio
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🎨 Customization

### Personal Information

1. **Update personal details** in the following files:
   - `src/app/page.tsx` - Hero section
   - `src/app/about/page.tsx` - About page content
   - `src/app/layout.tsx` - SEO metadata
   - `src/components/footer.tsx` - Footer links

2. **Replace placeholder content**:
   - Add your profile photo to `public/` folder
   - Update social media links
   - Replace project data with your actual projects
   - Add your CV file to `public/cv.pdf`

### Styling

- **Colors**: Modify CSS variables in `src/app/globals.css`
- **Fonts**: Change fonts in `src/app/layout.tsx`
- **Components**: Customize components in `src/components/`

### Adding New Sections

1. Create new page in `src/app/[section]/page.tsx`
2. Add navigation link in `src/components/header.tsx`
3. Update metadata in the page or layout file

## 📝 Content Guidelines

### Projects Section
Update the projects array in `src/app/projects/page.tsx`:

```typescript
const projects = [
  {
    id: 1,
    title: 'Your Project Name',
    description: 'Brief description of your project',
    image: '/project-image.jpg', // Add to public folder
    technologies: ['React', 'Next.js', 'TypeScript'],
    githubUrl: 'https://github.com/username/repo',
    liveUrl: 'https://your-project.vercel.app',
    date: '2024',
    featured: true
  },
  // Add more projects...
]
```

### Skills Section
Update skills in `src/app/about/page.tsx`:

```typescript
const skills = [
  {
    category: 'Frontend',
    icon: Globe,
    technologies: ['React', 'Next.js', 'TypeScript'] // Your skills
  },
  // Add more skill categories...
]
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy with zero configuration

### Other Platforms

- **Netlify**: `npm run build` then deploy `out/` folder
- **GitHub Pages**: Use `next export` for static export
- **Self-hosted**: Use `npm run build` and `npm start`

## 🔧 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📱 Responsive Design

The website is fully responsive with breakpoints:
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

## 🌙 Dark Mode

Dark mode is implemented using CSS variables and localStorage persistence. Users can:
- Toggle manually with the theme button
- Use system preference (default)
- Preference is saved across sessions

## 🔍 SEO Features

- Meta tags for all pages
- Open Graph tags for social sharing
- Twitter Card support
- Structured data markup
- Sitemap generation
- Robots.txt

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🆘 Support

If you have any questions or need help customizing the portfolio:

1. Check the documentation above
2. Look at the code comments
3. Create an issue on GitHub
4. Contact me directly

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing framework
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS
- [Lucide](https://lucide.dev/) for the beautiful icons
- [Vercel](https://vercel.com/) for hosting and deployment
