{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/src/app/loading.tsx"], "sourcesContent": ["export default function Loading() {\n  return (\n    <div className=\"flex items-center justify-center min-h-[50vh]\">\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"></div>\n        <p className=\"text-muted-foreground\">Loading...</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAI7C", "debugId": null}}]}