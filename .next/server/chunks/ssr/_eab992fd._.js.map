{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\nimport { Github, Linkedin, Twitter, Mail, Download, ArrowRight } from \"lucide-react\";\n\nconst socialLinks = [\n  {\n    name: 'GitHub',\n    href: 'https://github.com/yourusername',\n    icon: Github,\n  },\n  {\n    name: 'LinkedIn',\n    href: 'https://linkedin.com/in/yourusername',\n    icon: Linkedin,\n  },\n  {\n    name: 'Twitter',\n    href: 'https://twitter.com/yourusername',\n    icon: Twitter,\n  },\n  {\n    name: 'Email',\n    href: 'mailto:<EMAIL>',\n    icon: Mail,\n  },\n];\n\nexport default function Home() {\n  return (\n    <div className=\"flex flex-col\">\n      {/* Hero Section */}\n      <section className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-32\">\n        <div className=\"flex flex-col items-center text-center space-y-8\">\n          {/* Profile Image */}\n          <div className=\"relative\">\n            <div className=\"w-32 h-32 md:w-40 md:h-40 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 p-1\">\n              <div className=\"w-full h-full rounded-full bg-background flex items-center justify-center\">\n                <div className=\"w-28 h-28 md:w-36 md:h-36 rounded-full bg-muted flex items-center justify-center text-4xl md:text-5xl font-bold text-muted-foreground\">\n                  YN\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Name and Title */}\n          <div className=\"space-y-4\">\n            <h1 className=\"text-4xl md:text-6xl font-bold tracking-tight\">\n              Your Name\n            </h1>\n            <p className=\"text-xl md:text-2xl text-muted-foreground max-w-2xl\">\n              Full Stack Developer | JavaScript Enthusiast\n            </p>\n            <p className=\"text-lg text-muted-foreground max-w-3xl\">\n              Passionate about creating beautiful, functional, and user-friendly applications.\n              Specialized in React, Next.js, and modern web technologies.\n            </p>\n          </div>\n\n          {/* Social Links */}\n          <div className=\"flex items-center space-x-6\">\n            {socialLinks.map((link) => {\n              const Icon = link.icon;\n              return (\n                <a\n                  key={link.name}\n                  href={link.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"p-3 rounded-full bg-muted hover:bg-accent transition-colors\"\n                  aria-label={link.name}\n                >\n                  <Icon className=\"h-6 w-6\" />\n                </a>\n              );\n            })}\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 pt-4\">\n            <a\n              href=\"/cv.pdf\"\n              download\n              className=\"inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90\"\n            >\n              <Download className=\"mr-2 h-4 w-4\" />\n              Download CV\n            </a>\n            <Link\n              href=\"/projects\"\n              className=\"inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-3 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground\"\n            >\n              View Projects\n              <ArrowRight className=\"ml-2 h-4 w-4\" />\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Quick About Section */}\n      <section className=\"border-t border-border/40 bg-muted/50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <h3 className=\"text-lg font-semibold mb-2\">Frontend</h3>\n              <p className=\"text-muted-foreground\">React, Next.js, TypeScript, Tailwind CSS</p>\n            </div>\n            <div className=\"text-center\">\n              <h3 className=\"text-lg font-semibold mb-2\">Backend</h3>\n              <p className=\"text-muted-foreground\">Node.js, Express, PostgreSQL, MongoDB</p>\n            </div>\n            <div className=\"text-center\">\n              <h3 className=\"text-lg font-semibold mb-2\">Tools</h3>\n              <p className=\"text-muted-foreground\">Git, Docker, AWS, Vercel, Figma</p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;IACZ;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAwI;;;;;;;;;;;;;;;;;;;;;sCAQ7J,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgD;;;;;;8CAG9D,8OAAC;oCAAE,WAAU;8CAAsD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAOzD,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,8OAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,cAAY,KAAK,IAAI;8CAErB,cAAA,8OAAC;wCAAK,WAAU;;;;;;mCAPX,KAAK,IAAI;;;;;4BAUpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}]}