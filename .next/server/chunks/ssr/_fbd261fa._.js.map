{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/src/app/about/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { Code, Database, Globe, Smartphone, Server, Palette } from 'lucide-react'\n\nexport const metadata: Metadata = {\n  title: 'About - Your Name',\n  description: 'Learn more about my background, skills, and experience as a Full Stack Developer.',\n}\n\nconst skills = [\n  {\n    category: 'Frontend',\n    icon: Globe,\n    technologies: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Vue.js', 'HTML5', 'CSS3', 'JavaScript']\n  },\n  {\n    category: 'Backend',\n    icon: Server,\n    technologies: ['Node.js', 'Express.js', 'Python', 'Django', 'FastAPI', 'REST APIs', 'GraphQL']\n  },\n  {\n    category: 'Database',\n    icon: Database,\n    technologies: ['PostgreSQL', 'MongoDB', 'MySQL', 'Redis', 'Prisma', 'Mongoose']\n  },\n  {\n    category: 'Mobile',\n    icon: Smartphone,\n    technologies: ['React Native', 'Expo', 'Flutter', 'iOS', 'Android']\n  },\n  {\n    category: 'DevOps & Tools',\n    icon: Code,\n    technologies: ['Git', 'Docker', 'AWS', 'Vercel', 'GitHub Actions', 'Linux', 'Nginx']\n  },\n  {\n    category: 'Design',\n    icon: Palette,\n    technologies: ['Figma', 'Adobe XD', 'Photoshop', 'UI/UX Design', 'Responsive Design']\n  }\n]\n\nconst experience = [\n  {\n    title: 'Senior Full Stack Developer',\n    company: 'Tech Company Inc.',\n    period: '2022 - Present',\n    description: 'Led development of multiple web applications using React, Next.js, and Node.js. Mentored junior developers and improved team productivity by 30%.'\n  },\n  {\n    title: 'Full Stack Developer',\n    company: 'Startup Solutions',\n    period: '2020 - 2022',\n    description: 'Developed and maintained e-commerce platforms. Implemented CI/CD pipelines and reduced deployment time by 50%.'\n  },\n  {\n    title: 'Frontend Developer',\n    company: 'Digital Agency',\n    period: '2018 - 2020',\n    description: 'Created responsive websites and web applications for various clients. Specialized in React and modern CSS frameworks.'\n  }\n]\n\nexport default function About() {\n  return (\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n      {/* Header */}\n      <div className=\"text-center mb-16\">\n        <h1 className=\"text-4xl md:text-5xl font-bold tracking-tight mb-4\">\n          About Me\n        </h1>\n        <p className=\"text-xl text-muted-foreground max-w-3xl mx-auto\">\n          I'm a passionate Full Stack Developer with over 5 years of experience creating \n          digital solutions that make a difference.\n        </p>\n      </div>\n\n      {/* Bio Section */}\n      <section className=\"mb-16\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          <div>\n            <h2 className=\"text-3xl font-bold mb-6\">My Story</h2>\n            <div className=\"space-y-4 text-muted-foreground\">\n              <p>\n                Hello! I'm a Full Stack Developer based in [Your Location]. My journey into \n                programming started during my computer science studies, where I discovered \n                my passion for creating digital experiences that solve real-world problems.\n              </p>\n              <p>\n                Over the years, I've had the privilege of working with startups, agencies, \n                and established companies, helping them bring their ideas to life through \n                clean, efficient, and scalable code.\n              </p>\n              <p>\n                When I'm not coding, you can find me exploring new technologies, contributing \n                to open-source projects, or sharing my knowledge through blog posts and \n                community talks.\n              </p>\n            </div>\n          </div>\n          <div className=\"flex justify-center\">\n            <div className=\"w-80 h-80 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 p-1\">\n              <div className=\"w-full h-full rounded-lg bg-background flex items-center justify-center\">\n                <div className=\"text-6xl font-bold text-muted-foreground\">👨‍💻</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Skills Section */}\n      <section className=\"mb-16\">\n        <h2 className=\"text-3xl font-bold text-center mb-12\">Skills & Technologies</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {skills.map((skill) => {\n            const Icon = skill.icon\n            return (\n              <div key={skill.category} className=\"p-6 rounded-lg border border-border bg-card\">\n                <div className=\"flex items-center mb-4\">\n                  <Icon className=\"h-6 w-6 mr-3 text-primary\" />\n                  <h3 className=\"text-xl font-semibold\">{skill.category}</h3>\n                </div>\n                <div className=\"flex flex-wrap gap-2\">\n                  {skill.technologies.map((tech) => (\n                    <span\n                      key={tech}\n                      className=\"px-3 py-1 text-sm bg-muted rounded-full text-muted-foreground\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )\n          })}\n        </div>\n      </section>\n\n      {/* Experience Section */}\n      <section>\n        <h2 className=\"text-3xl font-bold text-center mb-12\">Experience</h2>\n        <div className=\"space-y-8\">\n          {experience.map((exp, index) => (\n            <div key={index} className=\"p-6 rounded-lg border border-border bg-card\">\n              <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-4\">\n                <div>\n                  <h3 className=\"text-xl font-semibold\">{exp.title}</h3>\n                  <p className=\"text-primary font-medium\">{exp.company}</p>\n                </div>\n                <span className=\"text-sm text-muted-foreground bg-muted px-3 py-1 rounded-full mt-2 md:mt-0 self-start\">\n                  {exp.period}\n                </span>\n              </div>\n              <p className=\"text-muted-foreground\">{exp.description}</p>\n            </div>\n          ))}\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEA,MAAM,SAAS;IACb;QACE,UAAU;QACV,MAAM,oMAAA,CAAA,QAAK;QACX,cAAc;YAAC;YAAS;YAAW;YAAc;YAAgB;YAAU;YAAS;YAAQ;SAAa;IAC3G;IACA;QACE,UAAU;QACV,MAAM,sMAAA,CAAA,SAAM;QACZ,cAAc;YAAC;YAAW;YAAc;YAAU;YAAU;YAAW;YAAa;SAAU;IAChG;IACA;QACE,UAAU;QACV,MAAM,0MAAA,CAAA,WAAQ;QACd,cAAc;YAAC;YAAc;YAAW;YAAS;YAAS;YAAU;SAAW;IACjF;IACA;QACE,UAAU;QACV,MAAM,8MAAA,CAAA,aAAU;QAChB,cAAc;YAAC;YAAgB;YAAQ;YAAW;YAAO;SAAU;IACrE;IACA;QACE,UAAU;QACV,MAAM,kMAAA,CAAA,OAAI;QACV,cAAc;YAAC;YAAO;YAAU;YAAO;YAAU;YAAkB;YAAS;SAAQ;IACtF;IACA;QACE,UAAU;QACV,MAAM,wMAAA,CAAA,UAAO;QACb,cAAc;YAAC;YAAS;YAAY;YAAa;YAAgB;SAAoB;IACvF;CACD;AAED,MAAM,aAAa;IACjB;QACE,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;IACf;IACA;QACE,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;IACf;IACA;QACE,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqD;;;;;;kCAGnE,8OAAC;wBAAE,WAAU;kCAAkD;;;;;;;;;;;;0BAOjE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDAKH,8OAAC;sDAAE;;;;;;sDAKH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAOP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpE,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC;4BACX,MAAM,OAAO,MAAM,IAAI;4BACvB,qBACE,8OAAC;gCAAyB,WAAU;;kDAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAG,WAAU;0DAAyB,MAAM,QAAQ;;;;;;;;;;;;kDAEvD,8OAAC;wCAAI,WAAU;kDACZ,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;;;;;;;+BARH,MAAM,QAAQ;;;;;wBAiB5B;;;;;;;;;;;;0BAKJ,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,KAAK,sBACpB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAyB,IAAI,KAAK;;;;;;kEAChD,8OAAC;wDAAE,WAAU;kEAA4B,IAAI,OAAO;;;;;;;;;;;;0DAEtD,8OAAC;gDAAK,WAAU;0DACb,IAAI,MAAM;;;;;;;;;;;;kDAGf,8OAAC;wCAAE,WAAU;kDAAyB,IAAI,WAAW;;;;;;;+BAV7C;;;;;;;;;;;;;;;;;;;;;;AAiBtB", "debugId": null}}]}