{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/src/components/theme-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\n\ntype Theme = 'dark' | 'light' | 'system'\n\ntype ThemeProviderProps = {\n  children: React.ReactNode\n  defaultTheme?: Theme\n  storageKey?: string\n}\n\ntype ThemeProviderState = {\n  theme: Theme\n  setTheme: (theme: Theme) => void\n}\n\nconst initialState: ThemeProviderState = {\n  theme: 'system',\n  setTheme: () => null,\n}\n\nconst ThemeProviderContext = createContext<ThemeProviderState>(initialState)\n\nexport function ThemeProvider({\n  children,\n  defaultTheme = 'system',\n  storageKey = 'vite-ui-theme',\n  ...props\n}: ThemeProviderProps) {\n  const [theme, setTheme] = useState<Theme>(defaultTheme)\n\n  // Load theme from localStorage on mount\n  useEffect(() => {\n    const storedTheme = localStorage?.getItem(storageKey) as Theme\n    if (storedTheme) {\n      setTheme(storedTheme)\n    }\n  }, [storageKey])\n\n  useEffect(() => {\n    const root = window.document.documentElement\n\n    root.classList.remove('light', 'dark')\n\n    if (theme === 'system') {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)')\n        .matches\n        ? 'dark'\n        : 'light'\n\n      root.classList.add(systemTheme)\n      return\n    }\n\n    root.classList.add(theme)\n  }, [theme])\n\n  const value = {\n    theme,\n    setTheme: (theme: Theme) => {\n      localStorage?.setItem(storageKey, theme)\n      setTheme(theme)\n    },\n  }\n\n  return (\n    <ThemeProviderContext.Provider {...props} value={value}>\n      {children}\n    </ThemeProviderContext.Provider>\n  )\n}\n\nexport const useTheme = () => {\n  const context = useContext(ThemeProviderContext)\n\n  if (context === undefined)\n    throw new Error('useTheme must be used within a ThemeProvider')\n\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAiBA,MAAM,eAAmC;IACvC,OAAO;IACP,UAAU,IAAM;AAClB;AAEA,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAsB;AAExD,SAAS,cAAc,EAC5B,QAAQ,EACR,eAAe,QAAQ,EACvB,aAAa,eAAe,EAC5B,GAAG,OACgB;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,cAAc,QAAQ;QAC1C,IAAI,aAAa;YACf,SAAS;QACX;IACF,GAAG;QAAC;KAAW;IAEf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;QAE5C,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;QAE/B,IAAI,UAAU,UAAU;YACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCACnC,OAAO,GACN,SACA;YAEJ,KAAK,SAAS,CAAC,GAAG,CAAC;YACnB;QACF;QAEA,KAAK,SAAS,CAAC,GAAG,CAAC;IACrB,GAAG;QAAC;KAAM;IAEV,MAAM,QAAQ;QACZ;QACA,UAAU,CAAC;YACT,cAAc,QAAQ,YAAY;YAClC,SAAS;QACX;IACF;IAEA,qBACE,8OAAC,qBAAqB,QAAQ;QAAE,GAAG,KAAK;QAAE,OAAO;kBAC9C;;;;;;AAGP;AAEO,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,YAAY,WACd,MAAM,IAAI,MAAM;IAElB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/src/components/header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Moon, Sun, Menu, X } from 'lucide-react'\nimport { useTheme } from './theme-provider'\nimport { useState } from 'react'\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Contact', href: '/contact' },\n]\n\nexport default function Header() {\n  const pathname = usePathname()\n  const { theme, setTheme } = useTheme()\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n\n  const toggleTheme = () => {\n    setTheme(theme === 'dark' ? 'light' : 'dark')\n  }\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-xl font-bold text-foreground\">\n              DevPortfolio\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`text-sm font-medium transition-colors hover:text-primary ${\n                  pathname === item.href\n                    ? 'text-primary'\n                    : 'text-muted-foreground'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Theme Toggle & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={toggleTheme}\n              className=\"inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground\"\n              aria-label=\"Toggle theme\"\n            >\n              {theme === 'dark' ? (\n                <Sun className=\"h-5 w-5\" />\n              ) : (\n                <Moon className=\"h-5 w-5\" />\n              )}\n            </button>\n\n            {/* Mobile menu button */}\n            <button\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n              className=\"inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground md:hidden\"\n              aria-label=\"Toggle menu\"\n            >\n              {mobileMenuOpen ? (\n                <X className=\"h-6 w-6\" />\n              ) : (\n                <Menu className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"space-y-1 px-2 pb-3 pt-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`block rounded-md px-3 py-2 text-base font-medium transition-colors hover:bg-accent hover:text-accent-foreground ${\n                    pathname === item.href\n                      ? 'bg-accent text-accent-foreground'\n                      : 'text-muted-foreground'\n                  }`}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAoC;;;;;;;;;;;sCAM/D,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,yDAAyD,EACnE,aAAa,KAAK,IAAI,GAClB,iBACA,yBACJ;8CAED,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAcpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEV,UAAU,uBACT,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;6DAEf,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAKpB,8OAAC;oCACC,SAAS,IAAM,kBAAkB,CAAC;oCAClC,WAAU;oCACV,cAAW;8CAEV,+BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOvB,gCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,gHAAgH,EAC1H,aAAa,KAAK,IAAI,GAClB,qCACA,yBACJ;gCACF,SAAS,IAAM,kBAAkB;0CAEhC,KAAK,IAAI;+BATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBhC", "debugId": null}}]}