{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/src/app/projects/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Image from 'next/image'\nimport { Github, ExternalLink, Calendar } from 'lucide-react'\n\nexport const metadata: Metadata = {\n  title: 'Projects - Your Name',\n  description: 'Explore my portfolio of web development projects and applications.',\n}\n\nconst projects = [\n  {\n    id: 1,\n    title: 'E-Commerce Platform',\n    description: 'A full-stack e-commerce solution built with Next.js, Stripe, and PostgreSQL. Features include user authentication, product management, shopping cart, and payment processing.',\n    image: '/project-1.jpg',\n    technologies: ['Next.js', 'TypeScript', 'PostgreSQL', 'Stripe', 'Tailwind CSS'],\n    githubUrl: 'https://github.com/yourusername/ecommerce-platform',\n    liveUrl: 'https://ecommerce-demo.vercel.app',\n    date: '2023',\n    featured: true\n  },\n  {\n    id: 2,\n    title: 'Task Management App',\n    description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',\n    image: '/project-2.jpg',\n    technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB', 'Express'],\n    githubUrl: 'https://github.com/yourusername/task-manager',\n    liveUrl: 'https://taskmanager-demo.vercel.app',\n    date: '2023',\n    featured: true\n  },\n  {\n    id: 3,\n    title: 'Weather Dashboard',\n    description: 'A responsive weather dashboard that displays current weather conditions and forecasts for multiple cities with beautiful data visualizations.',\n    image: '/project-3.jpg',\n    technologies: ['Vue.js', 'Chart.js', 'OpenWeather API', 'CSS3'],\n    githubUrl: 'https://github.com/yourusername/weather-dashboard',\n    liveUrl: 'https://weather-dashboard-demo.vercel.app',\n    date: '2022',\n    featured: false\n  },\n  {\n    id: 4,\n    title: 'Social Media Analytics',\n    description: 'A comprehensive analytics dashboard for social media metrics with data visualization and reporting features.',\n    image: '/project-4.jpg',\n    technologies: ['React', 'D3.js', 'Python', 'FastAPI', 'PostgreSQL'],\n    githubUrl: 'https://github.com/yourusername/social-analytics',\n    liveUrl: 'https://analytics-demo.vercel.app',\n    date: '2022',\n    featured: false\n  },\n  {\n    id: 5,\n    title: 'Recipe Sharing Platform',\n    description: 'A community-driven platform for sharing and discovering recipes with user ratings, comments, and meal planning features.',\n    image: '/project-5.jpg',\n    technologies: ['Next.js', 'Prisma', 'MySQL', 'NextAuth.js', 'Cloudinary'],\n    githubUrl: 'https://github.com/yourusername/recipe-platform',\n    liveUrl: 'https://recipes-demo.vercel.app',\n    date: '2021',\n    featured: false\n  },\n  {\n    id: 6,\n    title: 'Portfolio Website',\n    description: 'A modern, responsive portfolio website built with Next.js and Tailwind CSS, featuring dark mode and smooth animations.',\n    image: '/project-6.jpg',\n    technologies: ['Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],\n    githubUrl: 'https://github.com/yourusername/portfolio',\n    liveUrl: 'https://yourportfolio.vercel.app',\n    date: '2021',\n    featured: false\n  }\n]\n\nfunction ProjectCard({ project }: { project: typeof projects[0] }) {\n  return (\n    <div className=\"group rounded-lg border border-border bg-card overflow-hidden transition-all hover:shadow-lg\">\n      {/* Project Image */}\n      <div className=\"relative h-48 bg-muted overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center\">\n          <div className=\"text-4xl font-bold text-muted-foreground opacity-50\">\n            {project.title.split(' ').map(word => word[0]).join('')}\n          </div>\n        </div>\n        {project.featured && (\n          <div className=\"absolute top-4 left-4\">\n            <span className=\"bg-primary text-primary-foreground text-xs font-medium px-2 py-1 rounded-full\">\n              Featured\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Project Content */}\n      <div className=\"p-6\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <h3 className=\"text-xl font-semibold group-hover:text-primary transition-colors\">\n            {project.title}\n          </h3>\n          <div className=\"flex items-center text-sm text-muted-foreground\">\n            <Calendar className=\"h-4 w-4 mr-1\" />\n            {project.date}\n          </div>\n        </div>\n        \n        <p className=\"text-muted-foreground mb-4 line-clamp-3\">\n          {project.description}\n        </p>\n\n        {/* Technologies */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {project.technologies.map((tech) => (\n            <span\n              key={tech}\n              className=\"px-2 py-1 text-xs bg-muted rounded text-muted-foreground\"\n            >\n              {tech}\n            </span>\n          ))}\n        </div>\n\n        {/* Links */}\n        <div className=\"flex items-center space-x-4\">\n          <a\n            href={project.githubUrl}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center text-sm text-muted-foreground hover:text-primary transition-colors\"\n          >\n            <Github className=\"h-4 w-4 mr-1\" />\n            Code\n          </a>\n          <a\n            href={project.liveUrl}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center text-sm text-muted-foreground hover:text-primary transition-colors\"\n          >\n            <ExternalLink className=\"h-4 w-4 mr-1\" />\n            Live Demo\n          </a>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default function Projects() {\n  const featuredProjects = projects.filter(project => project.featured)\n  const otherProjects = projects.filter(project => !project.featured)\n\n  return (\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n      {/* Header */}\n      <div className=\"text-center mb-16\">\n        <h1 className=\"text-4xl md:text-5xl font-bold tracking-tight mb-4\">\n          My Projects\n        </h1>\n        <p className=\"text-xl text-muted-foreground max-w-3xl mx-auto\">\n          A collection of projects I've worked on, showcasing my skills in \n          full-stack development, UI/UX design, and problem-solving.\n        </p>\n      </div>\n\n      {/* Featured Projects */}\n      {featuredProjects.length > 0 && (\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold mb-8\">Featured Projects</h2>\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n            {featuredProjects.map((project) => (\n              <ProjectCard key={project.id} project={project} />\n            ))}\n          </div>\n        </section>\n      )}\n\n      {/* Other Projects */}\n      <section>\n        <h2 className=\"text-3xl font-bold mb-8\">Other Projects</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {otherProjects.map((project) => (\n            <ProjectCard key={project.id} project={project} />\n          ))}\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"mt-16 text-center\">\n        <div className=\"p-8 rounded-lg border border-border bg-muted/50\">\n          <h3 className=\"text-2xl font-bold mb-4\">Interested in working together?</h3>\n          <p className=\"text-muted-foreground mb-6\">\n            I'm always open to discussing new opportunities and interesting projects.\n          </p>\n          <a\n            href=\"/contact\"\n            className=\"inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90\"\n          >\n            Get in Touch\n          </a>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAW;YAAc;YAAc;YAAU;SAAe;QAC/E,WAAW;QACX,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAS;YAAW;YAAa;YAAW;SAAU;QACrE,WAAW;QACX,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAU;YAAY;YAAmB;SAAO;QAC/D,WAAW;QACX,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAS;YAAS;YAAU;YAAW;SAAa;QACnE,WAAW;QACX,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAW;YAAU;YAAS;YAAe;SAAa;QACzE,WAAW;QACX,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAW;YAAc;YAAgB;SAAgB;QACxE,WAAW;QACX,SAAS;QACT,MAAM;QACN,UAAU;IACZ;CACD;AAED,SAAS,YAAY,EAAE,OAAO,EAAmC;IAC/D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;oBAGvD,QAAQ,QAAQ,kBACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAgF;;;;;;;;;;;;;;;;;0BAQtG,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK;;;;;;0CAEhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,QAAQ,IAAI;;;;;;;;;;;;;kCAIjB,8OAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;kCAItB,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;kCASX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAM,QAAQ,SAAS;gCACvB,QAAO;gCACP,KAAI;gCACJ,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,8OAAC;gCACC,MAAM,QAAQ,OAAO;gCACrB,QAAO;gCACP,KAAI;gCACJ,WAAU;;kDAEV,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD;AAEe,SAAS;IACtB,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpE,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,QAAQ;IAElE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqD;;;;;;kCAGnE,8OAAC;wBAAE,WAAU;kCAAkD;;;;;;;;;;;;YAOhE,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;gCAA6B,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;;;;;;;;0BAOpC,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,8OAAC;gCAA6B,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;;;;;;;;0BAMlC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "file": "external-link.js", "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/node_modules/lucide-react/src/icons/external-link.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 3h6v6', key: '1q9fwt' }],\n  ['path', { d: 'M10 14 21 3', key: 'gplh6r' }],\n  ['path', { d: 'M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6', key: 'a6xqqp' }],\n];\n\n/**\n * @component @name ExternalLink\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgM2g2djYiIC8+CiAgPHBhdGggZD0iTTEwIDE0IDIxIDMiIC8+CiAgPHBhdGggZD0iTTE4IDEzdjZhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOGEyIDIgMCAwIDEgMi0yaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/external-link\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ExternalLink = createLucideIcon('external-link', __iconNode);\n\nexport default ExternalLink;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3F;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "file": "calendar.js", "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,GAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}