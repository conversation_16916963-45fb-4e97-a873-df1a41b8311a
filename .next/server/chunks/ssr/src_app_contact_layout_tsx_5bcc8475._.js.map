{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/working/duanyu/cleaning-booking/src/app/contact/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\nexport const metadata: Metadata = {\n  title: 'Contact - Your Name',\n  description: 'Get in touch with me for project collaborations, job opportunities, or just to say hello.',\n}\n\nexport default function ContactLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return children\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}