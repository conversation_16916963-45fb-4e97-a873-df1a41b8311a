[{"name": "hot-reloader", "duration": 46, "timestamp": 107417464756, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1749310816110, "traceId": "ef0779ea5377787e"}, {"name": "setup-dev-bundler", "duration": 455022, "timestamp": 107417328712, "id": 2, "parentId": 1, "tags": {}, "startTime": 1749310815974, "traceId": "ef0779ea5377787e"}, {"name": "run-instrumentation-hook", "duration": 12, "timestamp": 107417815686, "id": 4, "parentId": 1, "tags": {}, "startTime": 1749310816461, "traceId": "ef0779ea5377787e"}, {"name": "start-dev-server", "duration": 841488, "timestamp": 107416979457, "id": 1, "tags": {"cpus": "10", "platform": "darwin", "memory.freeMem": "90832896", "memory.totalMem": "17179869184", "memory.heapSizeLimit": "8640266240", "memory.rss": "267468800", "memory.heapTotal": "97009664", "memory.heapUsed": "65227488"}, "startTime": 1749310815625, "traceId": "ef0779ea5377787e"}, {"name": "compile-path", "duration": 1917684, "timestamp": 107438705832, "id": 7, "tags": {"trigger": "/"}, "startTime": 1749310837351, "traceId": "ef0779ea5377787e"}, {"name": "ensure-page", "duration": 1918417, "timestamp": 107438705392, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1749310837351, "traceId": "ef0779ea5377787e"}]