interface SkillBadgeProps {
  skill: string
  level?: 'beginner' | 'intermediate' | 'advanced' | 'expert'
}

export default function SkillBadge({ skill, level = 'intermediate' }: SkillBadgeProps) {
  const levelColors = {
    beginner: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    intermediate: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    advanced: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    expert: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
  }

  return (
    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-all hover:scale-105 ${levelColors[level]}`}>
      {skill}
    </span>
  )
}
