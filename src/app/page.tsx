import Image from "next/image";
import Link from "next/link";
import { Github, Linkedin, Twitter, Mail, Download, ArrowRight } from "lucide-react";

const socialLinks = [
  {
    name: 'GitHub',
    href: 'https://github.com/yourusername',
    icon: Github,
  },
  {
    name: 'LinkedIn',
    href: 'https://linkedin.com/in/yourusername',
    icon: Linkedin,
  },
  {
    name: 'Twitter',
    href: 'https://twitter.com/yourusername',
    icon: Twitter,
  },
  {
    name: 'Email',
    href: 'mailto:<EMAIL>',
    icon: Mail,
  },
];

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-32">
        <div className="flex flex-col items-center text-center space-y-8">
          {/* Profile Image */}
          <div className="relative">
            <div className="w-32 h-32 md:w-40 md:h-40 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 p-1">
              <div className="w-full h-full rounded-full bg-background flex items-center justify-center">
                <div className="w-28 h-28 md:w-36 md:h-36 rounded-full bg-muted flex items-center justify-center text-4xl md:text-5xl font-bold text-muted-foreground">
                  YN
                </div>
              </div>
            </div>
          </div>

          {/* Name and Title */}
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
              Your Name
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-2xl">
              Full Stack Developer | JavaScript Enthusiast
            </p>
            <p className="text-lg text-muted-foreground max-w-3xl">
              Passionate about creating beautiful, functional, and user-friendly applications.
              Specialized in React, Next.js, and modern web technologies.
            </p>
          </div>

          {/* Social Links */}
          <div className="flex items-center space-x-6">
            {socialLinks.map((link) => {
              const Icon = link.icon;
              return (
                <a
                  key={link.name}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-3 rounded-full bg-muted hover:bg-accent transition-colors"
                  aria-label={link.name}
                >
                  <Icon className="h-6 w-6" />
                </a>
              );
            })}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 pt-4">
            <a
              href="/cv.pdf"
              download
              className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90"
            >
              <Download className="mr-2 h-4 w-4" />
              Download CV
            </a>
            <Link
              href="/projects"
              className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-3 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground"
            >
              View Projects
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Quick About Section */}
      <section className="border-t border-border/40 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Frontend</h3>
              <p className="text-muted-foreground">React, Next.js, TypeScript, Tailwind CSS</p>
            </div>
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Backend</h3>
              <p className="text-muted-foreground">Node.js, Express, PostgreSQL, MongoDB</p>
            </div>
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Tools</h3>
              <p className="text-muted-foreground">Git, Docker, AWS, Vercel, Figma</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
