import { Metadata } from 'next'
import { Code, Database, Globe, Smartphone, Server, Palette } from 'lucide-react'

export const metadata: Metadata = {
  title: 'About - Your Name',
  description: 'Learn more about my background, skills, and experience as a Full Stack Developer.',
}

const skills = [
  {
    category: 'Frontend',
    icon: Globe,
    technologies: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Vue.js', 'HTML5', 'CSS3', 'JavaScript']
  },
  {
    category: 'Backend',
    icon: Server,
    technologies: ['Node.js', 'Express.js', 'Python', 'Django', 'FastAPI', 'REST APIs', 'GraphQL']
  },
  {
    category: 'Database',
    icon: Database,
    technologies: ['PostgreSQL', 'MongoDB', 'MySQL', 'Redis', 'Prisma', 'Mongoose']
  },
  {
    category: 'Mobile',
    icon: Smartphone,
    technologies: ['React Native', 'Expo', 'Flutter', 'iOS', 'Android']
  },
  {
    category: 'DevOps & Tools',
    icon: Code,
    technologies: ['Git', 'Docker', 'AWS', 'Vercel', 'GitHub Actions', 'Linux', 'Nginx']
  },
  {
    category: 'Design',
    icon: Palette,
    technologies: ['Figma', 'Adobe XD', 'Photoshop', 'UI/UX Design', 'Responsive Design']
  }
]

const experience = [
  {
    title: 'Senior Full Stack Developer',
    company: 'Tech Company Inc.',
    period: '2022 - Present',
    description: 'Led development of multiple web applications using React, Next.js, and Node.js. Mentored junior developers and improved team productivity by 30%.'
  },
  {
    title: 'Full Stack Developer',
    company: 'Startup Solutions',
    period: '2020 - 2022',
    description: 'Developed and maintained e-commerce platforms. Implemented CI/CD pipelines and reduced deployment time by 50%.'
  },
  {
    title: 'Frontend Developer',
    company: 'Digital Agency',
    period: '2018 - 2020',
    description: 'Created responsive websites and web applications for various clients. Specialized in React and modern CSS frameworks.'
  }
]

export default function About() {
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
          About Me
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          I'm a passionate Full Stack Developer with over 5 years of experience creating 
          digital solutions that make a difference.
        </p>
      </div>

      {/* Bio Section */}
      <section className="mb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl font-bold mb-6">My Story</h2>
            <div className="space-y-4 text-muted-foreground">
              <p>
                Hello! I'm a Full Stack Developer based in [Your Location]. My journey into 
                programming started during my computer science studies, where I discovered 
                my passion for creating digital experiences that solve real-world problems.
              </p>
              <p>
                Over the years, I've had the privilege of working with startups, agencies, 
                and established companies, helping them bring their ideas to life through 
                clean, efficient, and scalable code.
              </p>
              <p>
                When I'm not coding, you can find me exploring new technologies, contributing 
                to open-source projects, or sharing my knowledge through blog posts and 
                community talks.
              </p>
            </div>
          </div>
          <div className="flex justify-center">
            <div className="w-80 h-80 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 p-1">
              <div className="w-full h-full rounded-lg bg-background flex items-center justify-center">
                <div className="text-6xl font-bold text-muted-foreground">👨‍💻</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-12">Skills & Technologies</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {skills.map((skill) => {
            const Icon = skill.icon
            return (
              <div key={skill.category} className="p-6 rounded-lg border border-border bg-card">
                <div className="flex items-center mb-4">
                  <Icon className="h-6 w-6 mr-3 text-primary" />
                  <h3 className="text-xl font-semibold">{skill.category}</h3>
                </div>
                <div className="flex flex-wrap gap-2">
                  {skill.technologies.map((tech) => (
                    <span
                      key={tech}
                      className="px-3 py-1 text-sm bg-muted rounded-full text-muted-foreground"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      </section>

      {/* Experience Section */}
      <section>
        <h2 className="text-3xl font-bold text-center mb-12">Experience</h2>
        <div className="space-y-8">
          {experience.map((exp, index) => (
            <div key={index} className="p-6 rounded-lg border border-border bg-card">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold">{exp.title}</h3>
                  <p className="text-primary font-medium">{exp.company}</p>
                </div>
                <span className="text-sm text-muted-foreground bg-muted px-3 py-1 rounded-full mt-2 md:mt-0 self-start">
                  {exp.period}
                </span>
              </div>
              <p className="text-muted-foreground">{exp.description}</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  )
}
