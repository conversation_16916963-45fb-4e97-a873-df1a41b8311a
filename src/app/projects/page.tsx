import { Metadata } from 'next'
import Image from 'next/image'
import { Github, ExternalLink, Calendar } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Projects - Your Name',
  description: 'Explore my portfolio of web development projects and applications.',
}

const projects = [
  {
    id: 1,
    title: 'E-Commerce Platform',
    description: 'A full-stack e-commerce solution built with Next.js, Stripe, and PostgreSQL. Features include user authentication, product management, shopping cart, and payment processing.',
    image: '/project-1.jpg',
    technologies: ['Next.js', 'TypeScript', 'PostgreSQL', 'Stripe', 'Tailwind CSS'],
    githubUrl: 'https://github.com/yourusername/ecommerce-platform',
    liveUrl: 'https://ecommerce-demo.vercel.app',
    date: '2023',
    featured: true
  },
  {
    id: 2,
    title: 'Task Management App',
    description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
    image: '/project-2.jpg',
    technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB', 'Express'],
    githubUrl: 'https://github.com/yourusername/task-manager',
    liveUrl: 'https://taskmanager-demo.vercel.app',
    date: '2023',
    featured: true
  },
  {
    id: 3,
    title: 'Weather Dashboard',
    description: 'A responsive weather dashboard that displays current weather conditions and forecasts for multiple cities with beautiful data visualizations.',
    image: '/project-3.jpg',
    technologies: ['Vue.js', 'Chart.js', 'OpenWeather API', 'CSS3'],
    githubUrl: 'https://github.com/yourusername/weather-dashboard',
    liveUrl: 'https://weather-dashboard-demo.vercel.app',
    date: '2022',
    featured: false
  },
  {
    id: 4,
    title: 'Social Media Analytics',
    description: 'A comprehensive analytics dashboard for social media metrics with data visualization and reporting features.',
    image: '/project-4.jpg',
    technologies: ['React', 'D3.js', 'Python', 'FastAPI', 'PostgreSQL'],
    githubUrl: 'https://github.com/yourusername/social-analytics',
    liveUrl: 'https://analytics-demo.vercel.app',
    date: '2022',
    featured: false
  },
  {
    id: 5,
    title: 'Recipe Sharing Platform',
    description: 'A community-driven platform for sharing and discovering recipes with user ratings, comments, and meal planning features.',
    image: '/project-5.jpg',
    technologies: ['Next.js', 'Prisma', 'MySQL', 'NextAuth.js', 'Cloudinary'],
    githubUrl: 'https://github.com/yourusername/recipe-platform',
    liveUrl: 'https://recipes-demo.vercel.app',
    date: '2021',
    featured: false
  },
  {
    id: 6,
    title: 'Portfolio Website',
    description: 'A modern, responsive portfolio website built with Next.js and Tailwind CSS, featuring dark mode and smooth animations.',
    image: '/project-6.jpg',
    technologies: ['Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],
    githubUrl: 'https://github.com/yourusername/portfolio',
    liveUrl: 'https://yourportfolio.vercel.app',
    date: '2021',
    featured: false
  }
]

function ProjectCard({ project }: { project: typeof projects[0] }) {
  return (
    <div className="group rounded-lg border border-border bg-card overflow-hidden transition-all hover:shadow-lg">
      {/* Project Image */}
      <div className="relative h-48 bg-muted overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center">
          <div className="text-4xl font-bold text-muted-foreground opacity-50">
            {project.title.split(' ').map(word => word[0]).join('')}
          </div>
        </div>
        {project.featured && (
          <div className="absolute top-4 left-4">
            <span className="bg-primary text-primary-foreground text-xs font-medium px-2 py-1 rounded-full">
              Featured
            </span>
          </div>
        )}
      </div>

      {/* Project Content */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">
            {project.title}
          </h3>
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="h-4 w-4 mr-1" />
            {project.date}
          </div>
        </div>
        
        <p className="text-muted-foreground mb-4 line-clamp-3">
          {project.description}
        </p>

        {/* Technologies */}
        <div className="flex flex-wrap gap-2 mb-4">
          {project.technologies.map((tech) => (
            <span
              key={tech}
              className="px-2 py-1 text-xs bg-muted rounded text-muted-foreground"
            >
              {tech}
            </span>
          ))}
        </div>

        {/* Links */}
        <div className="flex items-center space-x-4">
          <a
            href={project.githubUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm text-muted-foreground hover:text-primary transition-colors"
          >
            <Github className="h-4 w-4 mr-1" />
            Code
          </a>
          <a
            href={project.liveUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm text-muted-foreground hover:text-primary transition-colors"
          >
            <ExternalLink className="h-4 w-4 mr-1" />
            Live Demo
          </a>
        </div>
      </div>
    </div>
  )
}

export default function Projects() {
  const featuredProjects = projects.filter(project => project.featured)
  const otherProjects = projects.filter(project => !project.featured)

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
          My Projects
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          A collection of projects I've worked on, showcasing my skills in 
          full-stack development, UI/UX design, and problem-solving.
        </p>
      </div>

      {/* Featured Projects */}
      {featuredProjects.length > 0 && (
        <section className="mb-16">
          <h2 className="text-3xl font-bold mb-8">Featured Projects</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {featuredProjects.map((project) => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </div>
        </section>
      )}

      {/* Other Projects */}
      <section>
        <h2 className="text-3xl font-bold mb-8">Other Projects</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {otherProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="mt-16 text-center">
        <div className="p-8 rounded-lg border border-border bg-muted/50">
          <h3 className="text-2xl font-bold mb-4">Interested in working together?</h3>
          <p className="text-muted-foreground mb-6">
            I'm always open to discussing new opportunities and interesting projects.
          </p>
          <a
            href="/contact"
            className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90"
          >
            Get in Touch
          </a>
        </div>
      </section>
    </div>
  )
}
